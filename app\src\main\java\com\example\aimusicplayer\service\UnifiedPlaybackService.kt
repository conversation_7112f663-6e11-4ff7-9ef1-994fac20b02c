package com.example.aimusicplayer.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.widget.Toast
import androidx.annotation.OptIn
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import androidx.media3.common.C
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaMetadata
import androidx.media3.common.Player
import androidx.media3.common.PlaybackException
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.session.DefaultMediaNotificationProvider
import androidx.media3.session.MediaSession
import androidx.media3.session.MediaSessionService
import com.example.aimusicplayer.MusicApplication
import com.example.aimusicplayer.R
import com.example.aimusicplayer.data.source.MusicDataSource
import com.example.aimusicplayer.service.PlayMode
import com.example.aimusicplayer.ui.main.MainActivity
import com.example.aimusicplayer.utils.Constants
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Random
import javax.inject.Inject

/**
 * 统一播放服务
 * 基于Media3的MediaSessionService实现
 * 负责音乐播放、播放队列管理、播放状态广播等
 */
@AndroidEntryPoint
class UnifiedPlaybackService : MediaSessionService() {

    companion object {
        // 播放控制广播Action
        const val ACTION_PLAY = "com.example.aimusicplayer.ACTION_PLAY"
        const val ACTION_PAUSE = "com.example.aimusicplayer.ACTION_PAUSE"
        const val ACTION_PREVIOUS = "com.example.aimusicplayer.ACTION_PREVIOUS"
        const val ACTION_NEXT = "com.example.aimusicplayer.ACTION_NEXT"
        const val ACTION_STOP = "com.example.aimusicplayer.ACTION_STOP"

        // 通知相关
        const val NOTIFICATION_ID = 1
        const val CHANNEL_ID = "music_playback_channel"

        val EXTRA_NOTIFICATION = "${UnifiedPlaybackService::class.java.packageName}.notification"
    }
    private val TAG = "UnifiedPlaybackService"
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    // 播放器和会话
    private lateinit var player: Player
    private lateinit var session: MediaSession
    private lateinit var notificationManager: NotificationManager

    // 播放队列
    private val playlist = mutableListOf<MediaItem>()
    private var currentIndex = -1

    // 播放模式
    private var playMode: PlayMode = PlayMode.Loop

    // 随机播放器
    private val random = Random()

    // 音频焦点
    private lateinit var audioManager: AudioManager
    private var audioFocusRequest: AudioFocusRequest? = null
    private var audioFocusChangeListener: AudioManager.OnAudioFocusChangeListener? = null
    private var hadAudioFocus = false

    // 状态流
    private val playStateFlow = MutableStateFlow<PlayState>(PlayState.Idle)
    private val progressFlow = MutableStateFlow(0L)
    private val bufferingPercentFlow = MutableStateFlow(0)
    private val playModeFlow = MutableStateFlow<PlayMode>(PlayMode.Loop)
    private val currentSongFlow = MutableStateFlow<MediaItem?>(null)
    private val playlistFlow = MutableStateFlow<List<MediaItem>>(emptyList())

    // 进度更新
    private val progressHandler = Handler(Looper.getMainLooper())
    private val progressRunnable = object : Runnable {
        override fun run() {
            if (player.isPlaying) {
                val position = player.currentPosition
                progressFlow.value = position
            }
            progressHandler.postDelayed(this, 500)
        }
    }

    // 播放控制广播接收器
    private val playbackControlReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action ?: return
            when (action) {
                ACTION_PLAY -> play()
                ACTION_PAUSE -> pause()
                ACTION_PREVIOUS -> playPrevious()
                ACTION_NEXT -> playNext()
                ACTION_STOP -> stop()
            }
        }
    }

    // 播放监听器
    private var playbackListener: PlaybackListener? = null

    @Inject
    lateinit var musicDataSource: MusicDataSource

    @OptIn(UnstableApi::class)
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "播放服务开始创建")

        try {
            // 注册服务实例到PlayServiceModule
            PlayServiceModule.setService(this)

            // 创建通知渠道
            createNotificationChannel()
            Log.d(TAG, "通知渠道创建完成")

            // 初始化通知管理器
            notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // 初始化音频管理器
            audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager

            // 初始化音频焦点
            initializeAudioFocus()
            Log.d(TAG, "音频焦点初始化完成")

            // 异步初始化ExoPlayer，避免阻塞主线程
            serviceScope.launch(Dispatchers.Main) {
                try {
                    initializePlayer()
                    Log.d(TAG, "播放器初始化完成")
                } catch (e: Exception) {
                    Log.e(TAG, "播放器初始化失败", e)
                }
            }

            // 注册广播接收器
            registerPlaybackControlReceiver()

            // 开始进度更新
            progressHandler.post(progressRunnable)

            // 异步加载保存的播放模式
            serviceScope.launch(Dispatchers.IO) {
                try {
                    loadPlayMode()
                    // 在主线程设置播放模式
                    serviceScope.launch(Dispatchers.Main) {
                        setPlayMode(playMode)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "加载播放模式失败", e)
                }
            }

            Log.d(TAG, "播放服务创建完成")
        } catch (e: Exception) {
            Log.e(TAG, "播放服务创建失败", e)
            throw e
        }
    }

    /**
     * 初始化播放器（异步执行）
     */
    @OptIn(UnstableApi::class)
    private suspend fun initializePlayer() = withContext(Dispatchers.Main) {
        try {
            player = ExoPlayer.Builder(applicationContext)
                // 自动处理音频焦点（我们手动处理，所以设为false）
                .setHandleAudioBecomingNoisy(true)
                // 设置播放模式
                .setWakeMode(C.WAKE_MODE_NETWORK)
                .setMediaSourceFactory(
                    DefaultMediaSourceFactory(applicationContext)
                        .setDataSourceFactory(MusicDataSource.Factory(applicationContext))
                )
                .build()

            // 设置播放器监听器
            setupPlayerListeners()

            // 创建媒体会话
            session = MediaSession.Builder(this@UnifiedPlaybackService, player)
                .setSessionActivity(
                    PendingIntent.getActivity(
                        this@UnifiedPlaybackService,
                        0,
                        Intent(this@UnifiedPlaybackService, MainActivity::class.java).apply {
                            putExtra(EXTRA_NOTIFICATION, true)
                            putExtra("OPEN_PLAYER_FRAGMENT", true) // 添加标记，指示打开PlayerFragment
                            action = Intent.ACTION_VIEW
                            addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                            addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
                            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        },
                        PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                    )
                )
                .build()

            // 设置通知提供者
            setMediaNotificationProvider(
                DefaultMediaNotificationProvider.Builder(applicationContext).build().apply {
                    setSmallIcon(R.drawable.ic_notification)
                }
            )

            // 设置播放器到PlayServiceModule
            PlayServiceModule.setPlayer(player)

            Log.d(TAG, "ExoPlayer初始化成功")
        } catch (e: Exception) {
            Log.e(TAG, "ExoPlayer初始化失败", e)
            throw e
        }
    }

    /**
     * 注册播放控制广播接收器
     */
    private fun registerPlaybackControlReceiver() {
        val filter = IntentFilter().apply {
            addAction(ACTION_PLAY)
            addAction(ACTION_PAUSE)
            addAction(ACTION_PREVIOUS)
            addAction(ACTION_NEXT)
            addAction(ACTION_STOP)
        }
        registerReceiver(playbackControlReceiver, filter)
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // 调用父类方法
        super.onStartCommand(intent, flags, startId)

        try {
            // 立即启动前台服务，避免ANR
            startForeground(Constants.NOTIFICATION_ID, createDefaultNotification())
            Log.d(TAG, "前台服务启动成功（使用默认通知）")

            // 等待播放器初始化完成后更新通知
            serviceScope.launch {
                // 等待播放器初始化完成
                var retryCount = 0
                while (!::player.isInitialized && retryCount < 50) { // 最多等待5秒
                    delay(100)
                    retryCount++
                }

                if (::player.isInitialized) {
                    // 更新通知
                    updateNotification()
                    Log.d(TAG, "播放器初始化完成，通知已更新")
                } else {
                    Log.e(TAG, "播放器初始化超时")
                }
            }

            // 处理播放控制Intent
            intent?.action?.let { action ->
                when (action) {
                    // 移除PLAY_INITIAL_SONG处理逻辑
                    ACTION_PLAY -> {
                        Log.d(TAG, "收到播放Intent")
                        serviceScope.launch {
                            // 等待播放器初始化
                            var retryCount = 0
                            while (!::player.isInitialized && retryCount < 50) {
                                delay(100)
                                retryCount++
                            }
                            if (::player.isInitialized) {
                                play()
                            }
                        }
                    }
                    ACTION_PAUSE -> {
                        Log.d(TAG, "收到暂停Intent")
                        if (::player.isInitialized) {
                            pause()
                        } else {
                            Log.w(TAG, "播放器未初始化，无法暂停")
                        }
                    }
                    ACTION_NEXT -> {
                        Log.d(TAG, "收到下一首Intent")
                        if (::player.isInitialized) {
                            playNext()
                        } else {
                            Log.w(TAG, "播放器未初始化，无法播放下一首")
                        }
                    }
                    ACTION_PREVIOUS -> {
                        Log.d(TAG, "收到上一首Intent")
                        if (::player.isInitialized) {
                            playPrevious()
                        } else {
                            Log.w(TAG, "播放器未初始化，无法播放上一首")
                        }
                    }
                    ACTION_STOP -> {
                        Log.d(TAG, "收到停止Intent")
                        if (::player.isInitialized) {
                            stop()
                        } else {
                            Log.w(TAG, "播放器未初始化，无法停止")
                        }
                    }
                    else -> {
                        Log.w(TAG, "未知的Intent action: $action")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "onStartCommand执行失败", e)
        }

        return START_NOT_STICKY
    }

    /**
     * 设置播放器监听器
     */
    private fun setupPlayerListeners() {
        player.addListener(object : Player.Listener {
            override fun onMediaItemTransition(mediaItem: MediaItem?, reason: Int) {
                super.onMediaItemTransition(mediaItem, reason)
                mediaItem?.let {
                    // 当切换到新的歌曲时，加载歌词
                    loadLyrics(it.mediaId)

                    // 更新当前歌曲状态流
                    currentSongFlow.value = it

                    // 保存播放历史
                    savePlayHistory(it)

                    // 更新通知
                    updateNotification()
                }
            }

            override fun onPlaybackStateChanged(playbackState: Int) {
                super.onPlaybackStateChanged(playbackState)
                // 更新播放状态流
                when (playbackState) {
                    Player.STATE_IDLE -> {
                        playStateFlow.value = PlayState.Idle
                        progressFlow.value = 0
                        bufferingPercentFlow.value = 0
                    }
                    Player.STATE_BUFFERING -> {
                        playStateFlow.value = PlayState.Preparing
                        bufferingPercentFlow.value = player.bufferedPercentage
                    }
                    Player.STATE_READY -> {
                        playStateFlow.value = if (player.isPlaying) PlayState.Playing else PlayState.Pause
                    }
                    Player.STATE_ENDED -> {
                        // 根据播放模式处理播放结束
                        handlePlaybackEnded()
                    }
                }

                // 更新通知
                updateNotification()
            }

            override fun onIsPlayingChanged(isPlaying: Boolean) {
                super.onIsPlayingChanged(isPlaying)
                // 更新播放状态流
                if (player.playbackState == Player.STATE_READY) {
                    playStateFlow.value = if (isPlaying) PlayState.Playing else PlayState.Pause
                }

                // 通知播放状态变化
                playbackListener?.onPlayStateChanged(isPlaying)

                // 更新通知
                updateNotification()
            }

            override fun onPlaylistMetadataChanged(mediaMetadata: MediaMetadata) {
                super.onPlaylistMetadataChanged(mediaMetadata)
                // 更新播放列表状态流
                playlistFlow.value = playlist
            }

            // 监听缓冲位置变化
            override fun onEvents(player: Player, events: Player.Events) {
                super.onEvents(player, events)
                // 检查是否有缓冲位置变化事件
                if (events.contains(Player.EVENT_POSITION_DISCONTINUITY)) {
                    // 更新缓冲百分比
                    val bufferedPosition = player.bufferedPosition
                    if (player.duration > 0) {
                        bufferingPercentFlow.value = (bufferedPosition * 100 / player.duration).toInt()
                    }
                }
            }

            @OptIn(UnstableApi::class)
            override fun onPlayerError(@Suppress("UNUSED_PARAMETER") error: PlaybackException) {
                super.onPlayerError(error)
                Log.e(TAG, "播放错误: ${error.errorCodeName}, ${error.localizedMessage}")

                // 更新播放状态为错误
                playStateFlow.value = PlayState.Error("播放错误: ${error.errorCodeName}, ${error.localizedMessage}")

                // 通知播放错误
                playbackListener?.onError("播放错误: ${error.errorCodeName}, ${error.localizedMessage}")

                // 尝试恢复播放
                handlePlaybackError(error)
            }
        })
    }

    /**
     * 处理播放结束
     */
    private fun handlePlaybackEnded() {
        // 检查播放列表是否为空
        if (playlist.isEmpty()) {
            Log.w(TAG, "播放列表为空，无法处理播放结束")
            return
        }

        when (playMode) {
            is PlayMode.Loop -> {
                // 列表循环，播放下一首
                playNext()
            }
            is PlayMode.Single -> {
                // 单曲循环，重新播放当前歌曲
                if (currentIndex >= 0 && currentIndex < playlist.size) {
                    prepareAndPlay(currentIndex)
                } else {
                    playNext()
                }
            }
            is PlayMode.Shuffle -> {
                // 随机播放，随机选择一首
                if (playlist.size > 0) {
                    val nextIndex = random.nextInt(playlist.size)
                    prepareAndPlay(nextIndex)
                }
            }
        }
    }

    /**
     * 处理播放错误
     */
    @Suppress("UNUSED_PARAMETER")
    private fun handlePlaybackError(error: PlaybackException) {
        // 尝试重新准备播放器
        try {
            if (currentIndex >= 0 && currentIndex < playlist.size) {
                // 暂停一下再重试
                Handler(Looper.getMainLooper()).postDelayed({
                    prepareAndPlay(currentIndex)
                }, 1000)
            }
        } catch (e: Exception) {
            Log.e(TAG, "恢复播放失败", e)
            // 如果恢复失败，尝试播放下一首
            playNext()
        }
    }

    /**
     * 保存播放历史
     */
    private fun savePlayHistory(mediaItem: MediaItem) {
        serviceScope.launch(Dispatchers.IO) {
            try {
                val songId = mediaItem.mediaId.toLongOrNull() ?: return@launch

                // 保存播放历史到数据库
                musicDataSource.savePlayHistory(songId, System.currentTimeMillis())
            } catch (e: Exception) {
                Log.e(TAG, "保存播放历史失败", e)
            }
        }
    }

    /**
     * 加载歌词
     */
    private fun loadLyrics(mediaId: String) {
        val songId = mediaId.toLongOrNull() ?: return

        serviceScope.launch {
            try {
                // 异步加载歌词
                val lyric = musicDataSource.getLyric(songId)
                Log.d(TAG, "歌词加载成功: ${lyric?.lrc?.lyric?.take(100)}...")
            } catch (e: Exception) {
                Log.e(TAG, "歌词加载失败", e)
            }
        }
    }

    /**
     * 初始化音频焦点
     */
    private fun initializeAudioFocus() {
        audioFocusChangeListener = AudioManager.OnAudioFocusChangeListener { focusChange ->
            when (focusChange) {
                AudioManager.AUDIOFOCUS_GAIN -> {
                    // 获得音频焦点
                    if (hadAudioFocus) {
                        player.play()
                    }
                    // 恢复音量
                    player.volume = 1.0f
                }
                AudioManager.AUDIOFOCUS_LOSS -> {
                    // 长时间失去音频焦点，停止播放
                    hadAudioFocus = player.isPlaying
                    player.pause()
                }
                AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> {
                    // 暂时失去音频焦点，暂停播放
                    hadAudioFocus = player.isPlaying
                    player.pause()
                }
                AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> {
                    // 暂时失去音频焦点，但可以继续播放，降低音量
                    player.volume = 0.3f
                }
            }
        }
    }

    /**
     * 请求音频焦点
     * @return 是否获得音频焦点
     */
    private fun requestAudioFocus(): Boolean {
        val result = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val audioAttributes = AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_MEDIA)
                .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                .build()

            audioFocusRequest = AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
                .setAudioAttributes(audioAttributes)
                .setAcceptsDelayedFocusGain(true)
                .setOnAudioFocusChangeListener(audioFocusChangeListener!!)
                .build()

            audioManager.requestAudioFocus(audioFocusRequest!!)
        } else {
            @Suppress("DEPRECATION")
            audioManager.requestAudioFocus(
                audioFocusChangeListener,
                AudioManager.STREAM_MUSIC,
                AudioManager.AUDIOFOCUS_GAIN
            )
        }

        return result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
    }

    /**
     * 放弃音频焦点
     */
    private fun abandonAudioFocus() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            audioFocusRequest?.let {
                audioManager.abandonAudioFocusRequest(it)
            }
        } else {
            @Suppress("DEPRECATION")
            audioFocusChangeListener?.let {
                audioManager.abandonAudioFocus(it)
            }
        }
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                Constants.NOTIFICATION_CHANNEL_ID,
                "音乐播放",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "音乐播放控制通知"
                setShowBadge(false)
                lockscreenVisibility = Notification.VISIBILITY_PUBLIC
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * 创建通知
     */
    private fun createNotification(): Notification {
        // 检查播放器是否已初始化
        if (!::player.isInitialized) {
            return createDefaultNotification()
        }

        // 获取当前播放的媒体项
        val mediaItem = if (currentIndex >= 0 && currentIndex < playlist.size) {
            playlist[currentIndex]
        } else {
            null
        }

        // 创建通知意图
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_SINGLE_TOP
            putExtra("OPEN_PLAYER_FRAGMENT", true) // 添加标记，指示打开PlayerFragment
        }

        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // 创建播放/暂停按钮意图
        val playPauseIntent = Intent(this, UnifiedPlaybackService::class.java).apply {
            action = if (player.isPlaying) ACTION_PAUSE else ACTION_PLAY
        }

        val playPausePendingIntent = PendingIntent.getService(
            this,
            1,
            playPauseIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // 创建上一首按钮意图
        val prevIntent = Intent(this, UnifiedPlaybackService::class.java).apply {
            action = ACTION_PREVIOUS
        }

        val prevPendingIntent = PendingIntent.getService(
            this,
            2,
            prevIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // 创建下一首按钮意图
        val nextIntent = Intent(this, UnifiedPlaybackService::class.java).apply {
            action = ACTION_NEXT
        }

        val nextPendingIntent = PendingIntent.getService(
            this,
            3,
            nextIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // 创建通知
        val builder = NotificationCompat.Builder(this, Constants.NOTIFICATION_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(mediaItem?.mediaMetadata?.title ?: "未知歌曲")
            .setContentText(mediaItem?.mediaMetadata?.artist ?: "未知艺术家")
            .setContentIntent(pendingIntent)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOnlyAlertOnce(true)
            .setOngoing(true)
            .addAction(
                R.drawable.ic_previous,
                "上一首",
                prevPendingIntent
            )
            .addAction(
                if (player.isPlaying) R.drawable.ic_pause else R.drawable.ic_play,
                if (player.isPlaying) "暂停" else "播放",
                playPausePendingIntent
            )
            .addAction(
                R.drawable.ic_next,
                "下一首",
                nextPendingIntent
            )
            .setStyle(
                androidx.media.app.NotificationCompat.MediaStyle()
                    .setShowActionsInCompactView(0, 1, 2)
            )

        return builder.build()
    }

    /**
     * 创建默认通知（播放器未初始化时使用）
     */
    private fun createDefaultNotification(): Notification {
        // 创建通知意图
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_SINGLE_TOP
            putExtra("OPEN_PLAYER_FRAGMENT", true)
        }

        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // 创建通知
        val builder = NotificationCompat.Builder(this, Constants.NOTIFICATION_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle("音乐播放器")
            .setContentText("正在初始化...")
            .setContentIntent(pendingIntent)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOnlyAlertOnce(true)
            .setOngoing(true)
            .setStyle(
                androidx.media.app.NotificationCompat.MediaStyle()
            )

        return builder.build()
    }

    /**
     * 更新通知
     */
    private fun updateNotification() {
        if (::player.isInitialized) {
            notificationManager.notify(Constants.NOTIFICATION_ID, createNotification())
        } else {
            notificationManager.notify(Constants.NOTIFICATION_ID, createDefaultNotification())
        }
    }

    /**
     * 加载播放模式
     */
    private fun loadPlayMode() {
        val sharedPreferences = getSharedPreferences(Constants.PREF_NAME, Context.MODE_PRIVATE)
        val mode = sharedPreferences.getInt(Constants.PREF_PLAY_MODE, PlayMode.Loop.value)
        playMode = PlayMode.valueOf(mode)
        playModeFlow.value = playMode
    }

    /**
     * 保存播放模式
     */
    private fun savePlayMode(mode: PlayMode) {
        val sharedPreferences = getSharedPreferences(Constants.PREF_NAME, Context.MODE_PRIVATE)
        sharedPreferences.edit().putInt(Constants.PREF_PLAY_MODE, mode.value).apply()
    }

    /**
     * 设置播放模式
     * @param mode 播放模式
     */
    fun setPlayMode(mode: PlayMode) {
        this.playMode = mode

        // 根据播放模式设置ExoPlayer的重复模式和随机播放
        when (mode) {
            is PlayMode.Loop -> {
                // 列表循环，播放完列表后从头开始
                player.repeatMode = Player.REPEAT_MODE_ALL
                player.shuffleModeEnabled = false
            }
            is PlayMode.Single -> {
                // 单曲循环，重复播放当前歌曲
                player.repeatMode = Player.REPEAT_MODE_ONE
                player.shuffleModeEnabled = false
            }
            is PlayMode.Shuffle -> {
                // 随机播放，随机顺序播放列表中的歌曲
                player.repeatMode = Player.REPEAT_MODE_ALL
                player.shuffleModeEnabled = true
            }
        }

        // 保存播放模式
        savePlayMode(mode)

        // 更新状态流
        playModeFlow.value = mode

        // 通知播放模式变化
        playbackListener?.onPlayModeChanged(mode)
    }

    /**
     * 切换播放模式
     */
    fun togglePlayMode() {
        val nextMode = when (playMode) {
            is PlayMode.Loop -> PlayMode.Single
            is PlayMode.Single -> PlayMode.Shuffle
            is PlayMode.Shuffle -> PlayMode.Loop
        }

        setPlayMode(nextMode)
    }

    /**
     * 准备并播放指定索引的歌曲
     */
    private fun prepareAndPlay(index: Int) {
        if (index >= 0 && index < playlist.size) {
            currentIndex = index

            // 停止当前播放
            player.stop()

            // 设置整个播放列表
            player.setMediaItems(playlist, index, 0)
            player.prepare()

            // 请求音频焦点
            if (requestAudioFocus()) {
                player.play()

                // 通知当前媒体项变化
                playbackListener?.onCurrentMediaItemChanged(playlist[index], index)
            } else {
                Toast.makeText(this, "无法获取音频焦点", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 播放
     */
    fun play() {
        if (currentIndex < 0 && playlist.isNotEmpty()) {
            currentIndex = 0
            prepareAndPlay(currentIndex)
        } else {
            if (requestAudioFocus()) {
                player.play()
            }
        }
    }

    /**
     * 暂停
     */
    fun pause() {
        player.pause()
    }

    /**
     * 播放/暂停切换
     */
    fun togglePlayPause() {
        if (player.isPlaying) {
            pause()
        } else {
            play()
        }
    }

    /**
     * 停止
     */
    fun stop() {
        player.stop()
        abandonAudioFocus()
    }

    /**
     * 播放下一首
     */
    fun playNext() {
        if (playlist.isEmpty()) {
            return
        }

        val nextIndex = when (playMode) {
            is PlayMode.Shuffle -> {
                // 随机模式下，随机选择一首
                random.nextInt(playlist.size)
            }
            else -> {
                // 其他模式下，播放下一首
                val next = currentIndex + 1

                // 如果是最后一首，则循环到第一首
                if (next >= playlist.size) 0 else next
            }
        }

        prepareAndPlay(nextIndex)
    }

    /**
     * 播放上一首
     */
    fun playPrevious() {
        if (playlist.isEmpty()) {
            return
        }

        val prevIndex = when (playMode) {
            is PlayMode.Shuffle -> {
                // 随机模式下，随机选择一首
                random.nextInt(playlist.size)
            }
            else -> {
                // 其他模式下，播放上一首
                val prev = currentIndex - 1

                // 如果是第一首，则循环到最后一首
                if (prev < 0) playlist.size - 1 else prev
            }
        }

        prepareAndPlay(prevIndex)
    }

    /**
     * 播放指定索引的歌曲
     */
    fun playAtIndex(index: Int) {
        if (index >= 0 && index < playlist.size) {
            prepareAndPlay(index)
        }
    }

    /**
     * 跳转到指定位置
     */
    fun seekTo(position: Long) {
        player.seekTo(position)
    }

    /**
     * 设置播放列表
     */
    fun setPlaylist(items: List<MediaItem>, startIndex: Int = 0) {
        playlist.clear()
        playlist.addAll(items)

        // 更新播放列表状态流
        playlistFlow.value = playlist.toList()

        // 通知播放列表变化
        playbackListener?.onPlaylistChanged(playlist)

        // 保存播放列表到数据库
        savePlaylistToDatabase()

        // 开始播放
        if (playlist.isNotEmpty()) {
            prepareAndPlay(startIndex)
        }
    }

    /**
     * 保存播放列表到数据库
     */
    private fun savePlaylistToDatabase() {
        serviceScope.launch(Dispatchers.IO) {
            try {
                // 将MediaItem列表转换为SongEntity列表
                val songEntities = playlist.mapNotNull { mediaItem ->
                    try {
                        val songId = mediaItem.mediaId.toLongOrNull() ?: return@mapNotNull null
                        val metadata = mediaItem.mediaMetadata

                        com.example.aimusicplayer.data.db.entity.SongEntity(
                            type = com.example.aimusicplayer.data.db.entity.SongEntity.TYPE_ONLINE,
                            songId = songId,
                            title = metadata.title?.toString() ?: "",
                            artist = metadata.artist?.toString() ?: "",
                            album = metadata.albumTitle?.toString() ?: "",
                            albumCover = metadata.artworkUri?.toString() ?: "",
                            duration = metadata.extras?.getLong("duration") ?: 0,
                            uri = mediaItem.requestMetadata.mediaUri?.toString() ?: ""
                        )
                    } catch (e: Exception) {
                        Log.e(TAG, "转换MediaItem到SongEntity失败", e)
                        null
                    }
                }

                // 保存到数据库
                if (songEntities.isNotEmpty()) {
                    musicDataSource.savePlaylist(songEntities)
                }
            } catch (e: Exception) {
                Log.e(TAG, "保存播放列表到数据库失败", e)
            }
        }
    }

    /**
     * 添加到播放列表
     */
    fun addToPlaylist(item: MediaItem) {
        playlist.add(item)

        // 更新播放列表状态流
        playlistFlow.value = playlist.toList()

        // 通知播放列表变化
        playbackListener?.onPlaylistChanged(playlist)

        // 保存播放列表到数据库
        savePlaylistToDatabase()
    }

    /**
     * 添加到播放列表
     */
    fun addToPlaylist(items: List<MediaItem>) {
        playlist.addAll(items)

        // 更新播放列表状态流
        playlistFlow.value = playlist.toList()

        // 通知播放列表变化
        playbackListener?.onPlaylistChanged(playlist)

        // 保存播放列表到数据库
        savePlaylistToDatabase()
    }

    /**
     * 添加并播放
     */
    fun addAndPlay(item: MediaItem) {
        // 检查是否已在播放列表中
        val index = playlist.indexOfFirst { it.mediaId == item.mediaId }

        if (index >= 0) {
            // 如果已在播放列表中，直接播放
            prepareAndPlay(index)
        } else {
            // 添加到播放列表
            playlist.add(item)

            // 更新播放列表状态流
            playlistFlow.value = playlist.toList()

            // 通知播放列表变化
            playbackListener?.onPlaylistChanged(playlist)

            // 保存播放列表到数据库
            savePlaylistToDatabase()

            // 播放新添加的歌曲
            prepareAndPlay(playlist.size - 1)
        }
    }

    /**
     * 从播放列表中移除
     */
    fun removeFromPlaylist(index: Int) {
        if (index >= 0 && index < playlist.size) {
            // 获取要删除的歌曲
            val removedItem = playlist[index]

            // 从列表中移除
            playlist.removeAt(index)

            // 更新播放列表状态流
            playlistFlow.value = playlist.toList()

            // 如果移除的是当前播放的歌曲，则播放下一首
            if (index == currentIndex) {
                if (playlist.isEmpty()) {
                    stop()
                    currentIndex = -1
                    currentSongFlow.value = null
                } else {
                    val newIndex = if (index >= playlist.size) playlist.size - 1 else index
                    prepareAndPlay(newIndex)
                }
            } else if (index < currentIndex) {
                // 如果移除的是当前播放歌曲之前的歌曲，则当前索引减1
                currentIndex--
            }

            // 通知播放列表变化
            playbackListener?.onPlaylistChanged(playlist)

            // 从数据库中删除
            serviceScope.launch(Dispatchers.IO) {
                try {
                    val songId = removedItem.mediaId.toLongOrNull() ?: return@launch
                    musicDataSource.removeFromPlaylist(songId)
                } catch (e: Exception) {
                    Log.e(TAG, "从数据库删除歌曲失败", e)
                }
            }
        }
    }

    /**
     * 清空播放列表
     */
    fun clearPlaylist() {
        playlist.clear()
        stop()
        currentIndex = -1

        // 更新状态流
        playlistFlow.value = emptyList()
        currentSongFlow.value = null

        // 通知播放列表变化
        playbackListener?.onPlaylistChanged(playlist)

        // 清空数据库中的播放列表
        serviceScope.launch(Dispatchers.IO) {
            try {
                musicDataSource.clearPlaylist()
            } catch (e: Exception) {
                Log.e(TAG, "清空数据库播放列表失败", e)
            }
        }
    }

    /**
     * 获取播放列表
     */
    fun getPlaylist(): List<MediaItem> {
        return playlist
    }

    /**
     * 获取当前索引
     */
    fun getCurrentIndex(): Int {
        return currentIndex
    }

    /**
     * 获取当前播放状态
     */
    fun getPlayState(): StateFlow<PlayState> {
        return playStateFlow
    }

    /**
     * 获取当前播放进度
     */
    fun getProgress(): StateFlow<Long> {
        return progressFlow
    }

    /**
     * 获取当前缓冲百分比
     */
    fun getBufferingPercent(): StateFlow<Int> {
        return bufferingPercentFlow
    }

    /**
     * 获取当前播放模式
     */
    fun getPlayMode(): StateFlow<PlayMode> {
        return playModeFlow
    }

    /**
     * 获取当前歌曲
     */
    fun getCurrentSong(): StateFlow<MediaItem?> {
        return currentSongFlow
    }

    /**
     * 获取播放列表
     */
    fun getPlaylistFlow(): StateFlow<List<MediaItem>> {
        return playlistFlow
    }

    /**
     * 设置播放监听器
     */
    fun setPlaybackListener(listener: PlaybackListener?) {
        this.playbackListener = listener
    }

    /**
     * 启动心动模式
     * @param songId 歌曲ID
     * @param playlistId 歌单ID，可选
     */
    fun startIntelligenceMode(songId: Long, playlistId: Long? = null) {
        serviceScope.launch {
            try {
                // 显示加载提示
                playbackListener?.onLoading(true, "正在加载心动模式...")

                // 调用API获取心动模式歌曲列表
                val result = musicDataSource.getIntelligenceList(songId, playlistId)

                if (result.isNotEmpty()) {
                    // 转换为MediaItem列表
                    val mediaItems = result.mapNotNull { song ->
                        try {
                            val metadata = MediaMetadata.Builder()
                                .setTitle(song.title)
                                .setArtist(song.artist)
                                .setAlbumTitle(song.album)
                                .setArtworkUri(android.net.Uri.parse(song.albumCover))
                                .setMediaType(MediaMetadata.MEDIA_TYPE_MUSIC)
                                .setIsBrowsable(false)
                                .setIsPlayable(true)
                                .build()

                            MediaItem.Builder()
                                .setMediaId(song.songId.toString())
                                .setMediaMetadata(metadata)
                                .setUri(song.uri)
                                .build()
                        } catch (e: Exception) {
                            Log.e(TAG, "转换歌曲到MediaItem失败", e)
                            null
                        }
                    }

                    if (mediaItems.isNotEmpty()) {
                        // 设置播放列表并开始播放
                        setPlaylist(mediaItems)

                        // 通知心动模式启动成功
                        playbackListener?.onIntelligenceModeStarted(mediaItems)
                    } else {
                        // 通知心动模式启动失败
                        playbackListener?.onError("心动模式启动失败：无法转换歌曲")
                    }
                } else {
                    // 通知心动模式启动失败
                    playbackListener?.onError("心动模式启动失败：未找到相似歌曲")
                }
            } catch (e: Exception) {
                Log.e(TAG, "心动模式启动失败", e)
                // 通知心动模式启动失败
                playbackListener?.onError("心动模式启动失败：${e.message}")
            } finally {
                // 隐藏加载提示
                playbackListener?.onLoading(false)
            }
        }
    }

    /**
     * 播放状态监听器接口
     */
    interface PlaybackListener {
        /**
         * 播放状态变化回调
         * @param isPlaying 是否正在播放
         */
        fun onPlayStateChanged(isPlaying: Boolean)

        /**
         * 播放位置变化回调
         * @param position 当前位置
         * @param duration 总时长
         */
        fun onPositionChanged(position: Long, duration: Long)

        /**
         * 播放模式变化回调
         * @param mode 播放模式
         */
        fun onPlayModeChanged(mode: PlayMode)

        /**
         * 播放列表变化回调
         * @param playlist 播放列表
         */
        fun onPlaylistChanged(playlist: List<MediaItem>)

        /**
         * 当前播放歌曲变化回调
         * @param mediaItem 当前播放的媒体项
         * @param position 在播放列表中的位置
         */
        fun onCurrentMediaItemChanged(mediaItem: MediaItem, position: Int)

        /**
         * 播放错误回调
         * @param error 错误信息
         */
        fun onError(error: String)

        /**
         * 加载状态回调
         * @param isLoading 是否正在加载
         * @param message 加载提示信息
         */
        fun onLoading(isLoading: Boolean, message: String = "")

        /**
         * 心动模式启动成功回调
         * @param playlist 心动模式播放列表
         */
        fun onIntelligenceModeStarted(playlist: List<MediaItem>)
    }

    override fun onGetSession(controllerInfo: MediaSession.ControllerInfo): MediaSession? {
        return session
    }

    override fun onTaskRemoved(rootIntent: Intent?) {
        super.onTaskRemoved(rootIntent)
        // 当应用被从任务列表移除时，停止播放
        player.stop()
    }

    override fun onDestroy() {
        super.onDestroy()
        // 停止进度更新
        progressHandler.removeCallbacks(progressRunnable)

        // 注销广播接收器
        try {
            unregisterReceiver(playbackControlReceiver)
        } catch (e: Exception) {
            Log.e(TAG, "注销广播接收器失败", e)
        }

        // 放弃音频焦点
        abandonAudioFocus()

        // 清理PlayServiceModule中的实例
        PlayServiceModule.clearInstances()

        // 释放资源
        player.release()
        session.release()
        Log.d(TAG, "播放服务已销毁")
    }

    // 已在类顶部定义了companion object，此处不需要重复定义
}
