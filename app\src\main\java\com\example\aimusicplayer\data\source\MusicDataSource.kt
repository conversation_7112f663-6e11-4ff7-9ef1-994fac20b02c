package com.example.aimusicplayer.data.source

import android.content.Context
import android.net.Uri
import android.util.Log
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaMetadata
import androidx.media3.datasource.DataSource
import androidx.media3.datasource.DefaultDataSource
import com.example.aimusicplayer.data.db.dao.PlayHistoryDao
import com.example.aimusicplayer.data.db.dao.PlaylistDao
import com.example.aimusicplayer.data.db.dao.SongDao
import com.example.aimusicplayer.data.db.entity.PlayHistoryEntity
import com.example.aimusicplayer.data.db.entity.PlaylistEntity
import com.example.aimusicplayer.data.db.entity.PlaylistSongCrossRef
import com.example.aimusicplayer.data.db.entity.SongEntity
import com.example.aimusicplayer.data.model.Comment
import com.example.aimusicplayer.data.model.CommentDto
import com.example.aimusicplayer.data.model.Reply
import com.example.aimusicplayer.data.model.Song
import com.example.aimusicplayer.data.model.BaseResponse // Added import
import com.example.aimusicplayer.data.model.SongDetailResponse // Added import
import java.util.Date
import com.example.aimusicplayer.utils.Constants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 音乐数据源
 * 负责从网络或本地获取音乐数据
 */
@Singleton
class MusicDataSource @Inject constructor(
    private val context: Context,
    private val okHttpClient: OkHttpClient,
    internal val songDao: SongDao,
    private val playlistDao: PlaylistDao,
    private val playHistoryDao: PlayHistoryDao
) {
    companion object {
        private const val TAG = "MusicDataSource"

        // 系统歌单ID
        const val PLAYLIST_ID_FAVORITE = 1L
        const val PLAYLIST_ID_RECENT = 2L
        const val PLAYLIST_ID_CURRENT = 3L
    }
    internal val apiService: ApiService by lazy {
        Retrofit.Builder()
            .baseUrl(Constants.BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            .create(ApiService::class.java)
    }

    /**
     * 将Song对象转换为MediaItem
     */
    fun songToMediaItem(song: Song): MediaItem {
        val albumCoverUrl = song.getAlbumCoverUrl() ?: ""
        Log.d(TAG, "songToMediaItem: ${song.name}, 专辑封面: $albumCoverUrl")

        val metadata = MediaMetadata.Builder()
            .setTitle(song.name)
            .setArtist(song.getArtistNames())
            .setAlbumTitle(song.getActualAlbum().name)
            .setArtworkUri(Uri.parse(albumCoverUrl))
            .build()

        return MediaItem.Builder()
            .setMediaId(song.id.toString())
            .setMediaMetadata(metadata)
            .setUri(getPlayUrl(song.id))
            .build()
    }

    /**
     * 获取播放URL
     */
    private fun getPlayUrl(id: Long): String {
        return "${Constants.BASE_URL}/song/url?id=$id"
    }

    /**
     * 获取歌曲详情 - 使用新的API接口获取完整信息包括高质量专辑封面
     */
    suspend fun getSongDetail(id: Long): Song? = withContext(Dispatchers.IO) {
        try {
            // 使用新的song/detail接口获取完整歌曲信息
            val response = apiService.getSongDetail(id.toString())
            if (response.code == 200 && response.songs.isNotEmpty()) {
                val song = response.songs[0]
                Log.d(TAG, "获取歌曲详情成功: ${song.name}, 专辑封面: ${song.al?.picUrl}")
                return@withContext song
            } else {
                Log.w(TAG, "获取歌曲详情失败: code=${response.code}, songs=${response.songs.size}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取歌曲详情异常", e)
        }
        return@withContext null
    }

    /**
     * 获取新歌速递
     * @param type 地区类型 id，全部:0, 华语:7, 欧美:96, 日本:8, 韩国:16
     */
    suspend fun getNewSongs(type: Int = 0): List<Song> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.getNewSongs(type)
            if (response.code == 200) {
                val songs = response.getSongs()
                Log.d(TAG, "获取新歌速递成功: 共${songs.size}首歌曲")
                return@withContext songs
            } else {
                Log.e(TAG, "获取新歌速递失败: code=${response.code}")
                throw Exception("API返回错误码: ${response.code}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取新歌速递失败", e)
            throw e // 抛出异常，让调用方处理UI提示
        }
    }

    /**
     * 获取歌词
     */
    suspend fun getLyric(id: Long): com.example.aimusicplayer.data.model.LyricResponse? = withContext(Dispatchers.IO) {
        try {
            val response = apiService.getLyric(id)
            if (response.code == 200) {
                return@withContext response
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return@withContext null
    }
/**
     * 获取Banner
     */
    suspend fun getBanners(): BaseResponse = withContext(Dispatchers.IO) {
        apiService.getBanners()
    }

    /**
     * 获取推荐歌单
     */
    suspend fun getRecommendPlaylists(): BaseResponse = withContext(Dispatchers.IO) {
        apiService.getRecommendPlaylists()
    }

    /**
     * 获取所有榜单
     */
    suspend fun getToplists(): BaseResponse = withContext(Dispatchers.IO) {
        apiService.getToplists()
    }

    /**
     * 获取新碟上架
     */
    suspend fun getNewAlbums(): BaseResponse = withContext(Dispatchers.IO) {
        apiService.getNewAlbums()
    }

    /**
     * 获取推荐歌曲
     */
    suspend fun getRecommendedSongs(): SongDetailResponse = withContext(Dispatchers.IO) {
        apiService.getRecommendedSongs()
    }

    /**
     * 保存播放历史
     */
    suspend fun savePlayHistory(songId: Long, playTime: Long = System.currentTimeMillis()) {
        try {
            // 查询歌曲
            val song = songDao.getSongById(songId, SongEntity.TYPE_ONLINE)

            if (song != null) {
                // 保存播放历史
                val history = PlayHistoryEntity(
                    songUniqueId = song.uniqueId,
                    playTime = playTime,
                    playPosition = 0,
                    playDuration = song.duration
                )
                playHistoryDao.insert(history)

                // 更新歌曲的最后播放时间
                songDao.update(song.copy(lastPlayedTime = playTime))

                // 添加到最近播放歌单
                addToRecentPlaylist(song.uniqueId)
            } else {
                // 如果歌曲不存在，先获取歌曲详情
                val songDetail = getSongDetail(songId)
                if (songDetail != null) {
                    // 保存歌曲
                    val newSong = songToEntity(songDetail)
                    songDao.insert(newSong)

                    // 保存播放历史
                    val history = PlayHistoryEntity(
                        songUniqueId = newSong.uniqueId,
                        playTime = playTime,
                        playPosition = 0,
                        playDuration = newSong.duration
                    )
                    playHistoryDao.insert(history)

                    // 添加到最近播放歌单
                    addToRecentPlaylist(newSong.uniqueId)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "保存播放历史失败", e)
        }
    }

    /**
     * 添加到最近播放歌单
     */
    private suspend fun addToRecentPlaylist(songUniqueId: String) {
        try {
            // 检查最近播放歌单是否存在
            var recentPlaylist = playlistDao.getPlaylistById(PLAYLIST_ID_RECENT)

            if (recentPlaylist == null) {
                // 创建最近播放歌单
                recentPlaylist = PlaylistEntity(
                    playlistId = PLAYLIST_ID_RECENT,
                    name = "最近播放",
                    coverUrl = "",
                    description = "自动生成的最近播放歌单",
                    isLocal = true
                )
                playlistDao.insert(recentPlaylist)
            }

            // 添加歌曲到最近播放歌单
            val crossRef = PlaylistSongCrossRef(
                playlistId = PLAYLIST_ID_RECENT,
                songUniqueId = songUniqueId,
                sortOrder = 0,
                addTime = System.currentTimeMillis()
            )
            playlistDao.addSongToPlaylist(crossRef)
        } catch (e: Exception) {
            Log.e(TAG, "添加到最近播放歌单失败", e)
        }
    }

    /**
     * 保存播放列表
     */
    suspend fun savePlaylist(songs: List<SongEntity>) {
        try {
            // 保存歌曲
            songDao.insertAll(songs)

            // 检查当前播放歌单是否存在
            var currentPlaylist = playlistDao.getPlaylistById(PLAYLIST_ID_CURRENT)

            if (currentPlaylist == null) {
                // 创建当前播放歌单
                currentPlaylist = PlaylistEntity(
                    playlistId = PLAYLIST_ID_CURRENT,
                    name = "当前播放",
                    coverUrl = "",
                    description = "当前播放列表",
                    isLocal = true
                )
                playlistDao.insert(currentPlaylist)
            }

            // 清空当前播放歌单
            playlistDao.clearPlaylist(PLAYLIST_ID_CURRENT)

            // 添加歌曲到当前播放歌单
            val crossRefs = songs.mapIndexed { index, song ->
                PlaylistSongCrossRef(
                    playlistId = PLAYLIST_ID_CURRENT,
                    songUniqueId = song.uniqueId,
                    sortOrder = index,
                    addTime = System.currentTimeMillis()
                )
            }
            playlistDao.addSongsToPlaylist(crossRefs)
        } catch (e: Exception) {
            Log.e(TAG, "保存播放列表失败", e)
        }
    }

    /**
     * 从播放列表中移除
     */
    suspend fun removeFromPlaylist(songId: Long) {
        try {
            val uniqueId = SongEntity.generateUniqueId(SongEntity.TYPE_ONLINE, songId)
            playlistDao.removeSongFromPlaylist(PLAYLIST_ID_CURRENT, uniqueId)
        } catch (e: Exception) {
            Log.e(TAG, "从播放列表中移除失败", e)
        }
    }

    /**
     * 清空播放列表
     */
    suspend fun clearPlaylist() {
        try {
            playlistDao.clearPlaylist(PLAYLIST_ID_CURRENT)
        } catch (e: Exception) {
            Log.e(TAG, "清空播放列表失败", e)
        }
    }

    /**
     * 获取最近播放的歌曲
     */
    fun getRecentPlayedSongs(limit: Int = 50): Flow<List<SongEntity>> {
        return playHistoryDao.getRecentPlayedSongs(limit)
    }

    /**
     * 获取收藏的歌曲
     */
    fun getFavoriteSongs(): Flow<List<SongEntity>> {
        return songDao.getAllFavoriteSongs()
    }

    /**
     * 收藏/取消收藏歌曲
     */
    suspend fun toggleFavorite(songId: Long): Boolean {
        try {
            val song = songDao.getSongById(songId, SongEntity.TYPE_ONLINE)

            if (song != null) {
                // 更新收藏状态
                val newFavoriteState = !song.isFavorite
                songDao.update(song.copy(isFavorite = newFavoriteState))

                // 更新收藏歌单
                if (newFavoriteState) {
                    // 添加到收藏歌单
                    addToFavoritePlaylist(song.uniqueId)
                } else {
                    // 从收藏歌单移除
                    removeFromFavoritePlaylist(song.uniqueId)
                }

                return newFavoriteState
            } else {
                // 如果歌曲不存在，先获取歌曲详情
                val songDetail = getSongDetail(songId)
                if (songDetail != null) {
                    // 保存歌曲
                    val newSong = songToEntity(songDetail, isFavorite = true)
                    songDao.insert(newSong)

                    // 添加到收藏歌单
                    addToFavoritePlaylist(newSong.uniqueId)

                    return true
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "收藏/取消收藏歌曲失败", e)
        }

        return false
    }

    /**
     * 添加到收藏歌单
     */
    private suspend fun addToFavoritePlaylist(songUniqueId: String) {
        try {
            // 检查收藏歌单是否存在
            var favoritePlaylist = playlistDao.getPlaylistById(PLAYLIST_ID_FAVORITE)

            if (favoritePlaylist == null) {
                // 创建收藏歌单
                favoritePlaylist = PlaylistEntity(
                    playlistId = PLAYLIST_ID_FAVORITE,
                    name = "我喜欢的音乐",
                    coverUrl = "",
                    description = "我收藏的歌曲",
                    isLocal = true
                )
                playlistDao.insert(favoritePlaylist)
            }

            // 添加歌曲到收藏歌单
            val crossRef = PlaylistSongCrossRef(
                playlistId = PLAYLIST_ID_FAVORITE,
                songUniqueId = songUniqueId,
                sortOrder = 0,
                addTime = System.currentTimeMillis()
            )
            playlistDao.addSongToPlaylist(crossRef)
        } catch (e: Exception) {
            Log.e(TAG, "添加到收藏歌单失败", e)
        }
    }

    /**
     * 从收藏歌单移除
     */
    private suspend fun removeFromFavoritePlaylist(songUniqueId: String) {
        try {
            playlistDao.removeSongFromPlaylist(PLAYLIST_ID_FAVORITE, songUniqueId)
        } catch (e: Exception) {
            Log.e(TAG, "从收藏歌单移除失败", e)
        }
    }

    /**
     * 获取心动模式歌曲列表
     */
    suspend fun getIntelligenceList(songId: Long, playlistId: Long? = null): List<SongEntity> {
        try {
            // 调用API获取心动模式歌曲列表
            val response = if (playlistId != null) {
                apiService.getIntelligenceList(songId, playlistId)
            } else {
                apiService.getSimiSongs(songId)
            }

            if (response.code == 200 && response.songs.isNotEmpty()) {
                // 转换为SongEntity列表
                val songs = response.songs.map { songToEntity(it) }

                // 保存到数据库
                songDao.insertAll(songs)

                return songs
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取心动模式歌曲列表失败", e)
        }

        return emptyList()
    }

    /**
     * 将Song对象转换为SongEntity
     */
    private fun songToEntity(song: Song, isFavorite: Boolean = false): SongEntity {
        val albumCoverUrl = song.getAlbumCoverUrl() ?: ""
        Log.d(TAG, "songToEntity: ${song.name}, 专辑封面: $albumCoverUrl")

        return SongEntity(
            type = SongEntity.TYPE_ONLINE,
            songId = song.id,
            title = song.name,
            artist = song.getArtistNames(),
            artistId = song.getActualArtists().firstOrNull()?.id ?: 0,
            album = song.getActualAlbum().name,
            albumId = song.getActualAlbum().id.toLongOrNull() ?: 0L,
            albumCover = albumCoverUrl,
            duration = song.dt,
            uri = getPlayUrl(song.id),
            isFavorite = isFavorite
        )
    }

    /**
     * 获取评论列表
     * @param songId 歌曲ID
     * @param offset 偏移量
     * @param limit 数量限制
     * @return 评论列表
     */
    suspend fun getComments(songId: Long, offset: Int = 0, limit: Int = 50): List<Comment> {
        return withContext(Dispatchers.IO) {
            try {
                // 从API获取评论
                val response = apiService.getComments(songId, limit, offset)

                if (response.code == 200) {
                    // 转换为Comment对象
                    response.comments?.map { commentDto ->
                        commentDto.toComment()
                    } ?: emptyList()
                } else {
                    emptyList()
                }
            } catch (e: Exception) {
                Log.e(TAG, "获取评论失败", e)
                emptyList()
            }
        }
    }

    /**
     * 发送评论
     * @param songId 歌曲ID
     * @param content 评论内容
     * @return 是否成功
     */
    suspend fun sendComment(songId: Long, content: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val response = apiService.sendComment(songId, content)
                response.code == 200
            } catch (e: Exception) {
                Log.e(TAG, "发送评论失败", e)
                false
            }
        }
    }

    /**
     * 点赞评论
     * @param commentId 评论ID
     * @return 是否成功
     */
    suspend fun likeComment(commentId: Long): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 默认为歌曲评论，type=0
                val response = apiService.likeComment(0, commentId, 1, 0)
                response.code == 200
            } catch (e: Exception) {
                Log.e(TAG, "点赞评论失败", e)
                false
            }
        }
    }

    /**
     * 获取热门评论
     * @param songId 歌曲ID
     * @param limit 数量限制
     * @return 热门评论列表
     */
    suspend fun getHotComments(songId: Long, limit: Int = 20): List<Comment> {
        return withContext(Dispatchers.IO) {
            try {
                // 从API获取热门评论
                val response = apiService.getHotComments(songId, limit)

                if (response.code == 200) {
                    // 转换为Comment对象
                    response.hotComments?.map { commentDto ->
                        commentDto.toComment()
                    } ?: emptyList()
                } else {
                    emptyList()
                }
            } catch (e: Exception) {
                Log.e(TAG, "获取热门评论失败", e)
                emptyList()
            }
        }
    }

    /**
     * 检查歌曲收藏状态
     * @param songId 歌曲ID
     * @return 是否已收藏
     */
    suspend fun checkLikeStatus(songId: Long): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 先从数据库查询
                val song = songDao.getSongById(songId, SongEntity.TYPE_ONLINE)
                if (song != null) {
                    return@withContext song.isFavorite
                }

                // 如果数据库中没有，从API查询
                val response = apiService.checkLikeStatus(songId)
                response.code == 200
            } catch (e: Exception) {
                Log.e(TAG, "检查收藏状态失败", e)
                false
            }
        }
    }

    /**
     * 收藏歌曲
     * @param songId 歌曲ID
     * @return 是否成功
     */
    suspend fun likeSong(songId: Long): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 调用API收藏歌曲
                val response = apiService.likeSong(songId)

                if (response.code == 200) {
                    // 更新数据库
                    val song = songDao.getSongById(songId, SongEntity.TYPE_ONLINE)
                    if (song != null) {
                        songDao.update(song.copy(isFavorite = true))
                        addToFavoritePlaylist(song.uniqueId)
                    } else {
                        // 如果歌曲不存在，先获取歌曲详情
                        val songDetail = getSongDetail(songId)
                        if (songDetail != null) {
                            // 保存歌曲
                            val newSong = songToEntity(songDetail, isFavorite = true)
                            songDao.insert(newSong)
                            addToFavoritePlaylist(newSong.uniqueId)
                        }
                    }
                    return@withContext true
                }
                false
            } catch (e: Exception) {
                Log.e(TAG, "收藏歌曲失败", e)
                false
            }
        }
    }

    /**
     * 取消收藏歌曲
     * @param songId 歌曲ID
     * @return 是否成功
     */
    suspend fun unlikeSong(songId: Long): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 调用API取消收藏歌曲
                val response = apiService.unlikeSong(songId)

                if (response.code == 200) {
                    // 更新数据库
                    val song = songDao.getSongById(songId, SongEntity.TYPE_ONLINE)
                    if (song != null) {
                        songDao.update(song.copy(isFavorite = false))
                        removeFromFavoritePlaylist(song.uniqueId)
                    }
                    return@withContext true
                }
                false
            } catch (e: Exception) {
                Log.e(TAG, "取消收藏歌曲失败", e)
                false
            }
        }
    }

    /**
     * 获取相似歌曲
     * @param songId 歌曲ID
     * @param limit 数量限制
     * @return 相似歌曲列表
     */
    suspend fun getSimilarSongs(songId: Long, @Suppress("UNUSED_PARAMETER") limit: Int = 50): List<Song> {
        return withContext(Dispatchers.IO) {
            try {
                // 调用API获取相似歌曲
                val response = apiService.getSimiSongs(songId)

                if (response.code == 200) {
                    return@withContext response.songs
                }
                emptyList()
            } catch (e: Exception) {
                Log.e(TAG, "获取相似歌曲失败", e)
                emptyList()
            }
        }
    }

    /**
     * ExoPlayer的DataSource.Factory实现
     */
    /**
     * 从数据库获取歌曲
     */
    suspend fun getSongByIdFromDb(songId: Long, type: Int): SongEntity? {
        return withContext(Dispatchers.IO) {
            songDao.getSongById(songId, type)
        }
    }

    /**
     * 搜索歌曲
     * @param keywords 搜索关键词
     * @param limit 数量限制
     * @param offset 偏移量
     * @return 搜索结果歌曲列表
     */
    suspend fun searchSongs(
        keywords: String,
        limit: Int = 30,
        offset: Int = 0
    ): List<Song> = withContext(Dispatchers.IO) {
        try {
            // 使用云搜索API获取更全面的结果
            val response = apiService.cloudSearch(keywords, 1, limit, offset)
            if (response.code == 200) {
                val songs = response.getSongs()
                Log.d(TAG, "搜索歌曲成功: 关键词=$keywords, 结果=${songs.size}首")
                return@withContext songs
            } else {
                Log.e(TAG, "搜索歌曲失败: code=${response.code}")
                throw Exception("搜索失败: ${response.code}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "搜索歌曲异常", e)
            throw e
        }
    }

    /**
     * 获取搜索建议
     * @param keywords 搜索关键词
     * @return 搜索建议列表
     */
    suspend fun getSearchSuggestions(keywords: String): List<String> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.searchSuggest(keywords)
            if (response.code == 200) {
                // 解析搜索建议，提取歌曲、歌手、专辑名称
                val suggestions = mutableListOf<String>()

                // 从响应中提取建议项
                val result = response.get("result")
                if (result != null) {
                    // 提取歌曲建议
                    val songs = result.get("songs")?.asJsonArray
                    songs?.forEach { songElement ->
                        val song = songElement.asJsonObject
                        val name = song.get("name")?.asString
                        if (!name.isNullOrEmpty()) {
                            suggestions.add(name)
                        }
                    }

                    // 提取歌手建议
                    val artists = result.get("artists")?.asJsonArray
                    artists?.forEach { artistElement ->
                        val artist = artistElement.asJsonObject
                        val name = artist.get("name")?.asString
                        if (!name.isNullOrEmpty()) {
                            suggestions.add(name)
                        }
                    }

                    // 提取专辑建议
                    val albums = result.get("albums")?.asJsonArray
                    albums?.forEach { albumElement ->
                        val album = albumElement.asJsonObject
                        val name = album.get("name")?.asString
                        if (!name.isNullOrEmpty()) {
                            suggestions.add(name)
                        }
                    }
                }

                Log.d(TAG, "获取搜索建议成功: 关键词=$keywords, 建议=${suggestions.size}个")
                return@withContext suggestions.take(10) // 限制返回10个建议
            } else {
                Log.w(TAG, "获取搜索建议失败: code=${response.code}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取搜索建议异常", e)
        }
        return@withContext emptyList()
    }

    /**
     * ExoPlayer的DataSource.Factory实现
     */
    class Factory(private val context: Context) : DataSource.Factory {
        private val defaultDataSourceFactory = DefaultDataSource.Factory(context)

        override fun createDataSource(): DataSource {
            return defaultDataSourceFactory.createDataSource()
        }
    }
}
